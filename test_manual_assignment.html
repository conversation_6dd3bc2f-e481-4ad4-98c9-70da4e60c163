<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Team Assignment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .toggle-checkbox {
            display: none;
        }
        .toggle-label {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            background-color: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .toggle-checkbox:checked + .toggle-label {
            background-color: #4CAF50;
        }
        .toggle-switch {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        .toggle-checkbox:checked + .toggle-label .toggle-switch {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <h1>Manual Team Assignment Feature Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Toggle UI Component</h2>
        <p>Testing the manual team assignment toggle switch:</p>
        <div class="toggle-container">
            <input type="checkbox" id="testToggle" class="toggle-checkbox">
            <label for="testToggle" class="toggle-label">
                <span class="toggle-switch"></span>
            </label>
            <span>Enable manual team selection for each question</span>
        </div>
        <button onclick="testToggleUI()">Test Toggle</button>
        <div id="toggleResult" class="test-result info">Click "Test Toggle" to verify the toggle works</div>
    </div>

    <div class="test-section">
        <h2>Test 2: Round Data Structure</h2>
        <p>Testing if rounds can store the manual team assignment property:</p>
        <button onclick="testRoundDataStructure()">Test Round Data</button>
        <div id="roundDataResult" class="test-result info">Click "Test Round Data" to verify data structure</div>
    </div>

    <div class="test-section">
        <h2>Test 3: Migration Logic</h2>
        <p>Testing if existing rounds get the new property added:</p>
        <button onclick="testMigrationLogic()">Test Migration</button>
        <div id="migrationResult" class="test-result info">Click "Test Migration" to verify migration works</div>
    </div>

    <div class="test-section">
        <h2>Test 4: Export/Import Compatibility</h2>
        <p>Testing if the new property is preserved in export/import:</p>
        <button onclick="testExportImport()">Test Export/Import</button>
        <div id="exportImportResult" class="test-result info">Click "Test Export/Import" to verify compatibility</div>
    </div>

    <div class="test-section">
        <h2>Instructions for Manual Testing</h2>
        <div class="info test-result">
            <h3>To test the full functionality:</h3>
            <ol>
                <li>Open the main Bible Quiz application</li>
                <li>Go to Rounds section and create a new round</li>
                <li>Enable the "Enable manual team selection" toggle</li>
                <li>Add some questions to the round</li>
                <li>Create some teams</li>
                <li>Start the quiz and select the round with manual assignment</li>
                <li>Verify that you see a team selection interface instead of automatic assignment</li>
                <li>Test that automatic assignment still works for rounds without the toggle</li>
                <li>Export and import the data to verify the setting is preserved</li>
            </ol>
        </div>
    </div>

    <script>
        function testToggleUI() {
            const toggle = document.getElementById('testToggle');
            const result = document.getElementById('toggleResult');
            
            // Test toggle functionality
            const initialState = toggle.checked;
            toggle.checked = !initialState;
            
            if (toggle.checked !== initialState) {
                result.className = 'test-result pass';
                result.textContent = '✓ PASS: Toggle UI is working correctly';
            } else {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Toggle UI is not working';
            }
        }

        function testRoundDataStructure() {
            const result = document.getElementById('roundDataResult');
            
            try {
                // Test creating a round object with the new property
                const testRound = {
                    name: "Test Round",
                    type: "questions",
                    timerDuration: 30,
                    timerSound: "clock_timer.mp3",
                    manualTeamAssignment: true,
                    questions: []
                };
                
                // Verify the property exists and has the correct value
                if (testRound.hasOwnProperty('manualTeamAssignment') && 
                    typeof testRound.manualTeamAssignment === 'boolean') {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: Round data structure supports manualTeamAssignment property';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: Round data structure issue';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error creating round data structure: ' + error.message;
            }
        }

        function testMigrationLogic() {
            const result = document.getElementById('migrationResult');
            
            try {
                // Simulate an old round without the new property
                const oldRound = {
                    name: "Old Round",
                    type: "questions",
                    timerDuration: 30,
                    timerSound: "clock_timer.mp3",
                    questions: []
                };
                
                // Simulate migration logic
                if (typeof oldRound.manualTeamAssignment === 'undefined') {
                    oldRound.manualTeamAssignment = false; // Default to automatic assignment
                }
                
                // Verify migration worked
                if (oldRound.hasOwnProperty('manualTeamAssignment') && 
                    oldRound.manualTeamAssignment === false) {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: Migration logic adds manualTeamAssignment property with default value';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: Migration logic not working correctly';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error in migration logic: ' + error.message;
            }
        }

        function testExportImport() {
            const result = document.getElementById('exportImportResult');
            
            try {
                // Simulate export data
                const exportData = {
                    rounds: [
                        {
                            name: "Manual Assignment Round",
                            type: "questions",
                            timerDuration: 30,
                            timerSound: "clock_timer.mp3",
                            manualTeamAssignment: true,
                            questions: []
                        },
                        {
                            name: "Auto Assignment Round",
                            type: "questions",
                            timerDuration: 30,
                            timerSound: "clock_timer.mp3",
                            manualTeamAssignment: false,
                            questions: []
                        }
                    ],
                    teams: [],
                    settings: {},
                    version: '1.2'
                };
                
                // Simulate JSON export/import
                const jsonString = JSON.stringify(exportData);
                const importedData = JSON.parse(jsonString);
                
                // Verify the property is preserved
                const manualRound = importedData.rounds.find(r => r.name === "Manual Assignment Round");
                const autoRound = importedData.rounds.find(r => r.name === "Auto Assignment Round");
                
                if (manualRound && manualRound.manualTeamAssignment === true &&
                    autoRound && autoRound.manualTeamAssignment === false) {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: Export/Import preserves manualTeamAssignment property correctly';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: Export/Import does not preserve the property correctly';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error in export/import test: ' + error.message;
            }
        }
    </script>
</body>
</html>
