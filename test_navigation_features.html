<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Features Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .feature {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .step-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .step-list li {
            margin: 8px 0;
        }
        .button-demo {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 30px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        .btn.secondary {
            background: #6c757d;
            color: white;
        }
        .btn.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
            font-weight: 600;
        }
        .btn.skyblue {
            background: linear-gradient(to right, #45b7d8, #2196f3);
            color: white;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .checklist {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .checklist ul {
            list-style-type: none;
            padding-left: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        .checklist li:before {
            content: "☐";
            position: absolute;
            left: 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>🧭 Navigation Features Test Guide</h1>
    
    <div class="test-section">
        <h2>🎯 New Features Added</h2>
        
        <div class="feature">
            <h3>1. Previous Question Button</h3>
            <p><strong>Purpose:</strong> Go back to the previously asked question</p>
            <p><strong>Location:</strong> Left side of quiz controls</p>
            <p><strong>Icon:</strong> ← Previous</p>
            <p><strong>Behavior:</strong> Restores previous question state, removes from skipped list if applicable</p>
        </div>

        <div class="feature">
            <h3>2. Skip Question Button</h3>
            <p><strong>Purpose:</strong> Skip current question without answering</p>
            <p><strong>Location:</strong> Center of quiz controls</p>
            <p><strong>Icon:</strong> ⏭ Skip</p>
            <p><strong>Behavior:</strong> Moves to next question, adds current to skipped list</p>
        </div>

        <div class="feature">
            <h3>3. Navigation History Tracking</h3>
            <p><strong>Purpose:</strong> Track question progression for Previous functionality</p>
            <p><strong>Storage:</strong> Saved in quiz state, compatible with export/import</p>
            <p><strong>Limit:</strong> Last 50 questions to prevent memory issues</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🎮 Button Layout Demo</h2>
        <p>This is how the new quiz controls should look:</p>
        
        <div class="button-demo">
            <button class="btn skyblue">▶ Start Timer</button>
            <button class="btn secondary" disabled>← Previous</button>
            <button class="btn warning" disabled>⏭ Skip</button>
            <button class="btn skyblue" disabled>Next Question →</button>
        </div>
        
        <div class="info test-result">
            <strong>Button States:</strong>
            <ul>
                <li><strong>Previous:</strong> Disabled when no history, enabled when questions have been answered</li>
                <li><strong>Skip:</strong> Enabled during active questions, disabled when question answered</li>
                <li><strong>Next:</strong> Enabled when question answered, disabled otherwise</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Comprehensive Testing Steps</h2>
        
        <div class="step-list">
            <h3>Phase 1: Basic Navigation Setup</h3>
            <ol>
                <li><strong>Create Test Round:</strong>
                    <ul>
                        <li>Add at least 5 questions with distinct content</li>
                        <li>Questions should have different correct answers for easy identification</li>
                    </ul>
                </li>
                <li><strong>Start Quiz:</strong>
                    <ul>
                        <li>Verify all 4 buttons are visible in quiz controls</li>
                        <li>Check initial button states (Previous disabled, Skip disabled, Next disabled)</li>
                    </ul>
                </li>
                <li><strong>Load First Question:</strong>
                    <ul>
                        <li>Click "Start Timer"</li>
                        <li>Verify Previous still disabled (no history)</li>
                        <li>Verify Skip now enabled</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="step-list">
            <h3>Phase 2: Skip Question Testing</h3>
            <ol>
                <li><strong>Skip First Question:</strong>
                    <ul>
                        <li>Click "Skip" button</li>
                        <li>Verify moves to next question</li>
                        <li>Verify Previous button now enabled</li>
                        <li>Verify question counter increments</li>
                    </ul>
                </li>
                <li><strong>Answer Second Question:</strong>
                    <ul>
                        <li>Click "Start Timer"</li>
                        <li>Select correct answer</li>
                        <li>Verify points awarded</li>
                        <li>Verify "Next Question" button appears</li>
                    </ul>
                </li>
                <li><strong>Move to Third Question:</strong>
                    <ul>
                        <li>Click "Next Question"</li>
                        <li>Verify Previous button still enabled</li>
                        <li>Verify navigation history is building</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="step-list">
            <h3>Phase 3: Previous Question Testing</h3>
            <ol>
                <li><strong>Go Back to Previous Question:</strong>
                    <ul>
                        <li>Click "Previous" button</li>
                        <li>Verify returns to second question (the answered one)</li>
                        <li>Verify question text and options match</li>
                        <li>Verify team assignment is correct</li>
                    </ul>
                </li>
                <li><strong>Go Back Again:</strong>
                    <ul>
                        <li>Click "Previous" button again</li>
                        <li>Verify returns to first question (the skipped one)</li>
                        <li>Verify question is now available to answer</li>
                        <li>Verify Skip button is enabled again</li>
                    </ul>
                </li>
                <li><strong>Test Previous Limit:</strong>
                    <ul>
                        <li>Click "Previous" button again</li>
                        <li>Verify button becomes disabled (no more history)</li>
                        <li>Verify stays on first question</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="step-list">
            <h3>Phase 4: Advanced Navigation Testing</h3>
            <ol>
                <li><strong>Mixed Navigation Pattern:</strong>
                    <ul>
                        <li>Answer first question → Next → Skip second → Answer third → Previous → Previous</li>
                        <li>Verify each step works correctly</li>
                        <li>Verify button states update appropriately</li>
                    </ul>
                </li>
                <li><strong>Team Assignment Testing:</strong>
                    <ul>
                        <li>Test with automatic team assignment</li>
                        <li>Test with manual team assignment (if enabled)</li>
                        <li>Verify Previous restores correct team</li>
                    </ul>
                </li>
                <li><strong>Timer Integration:</strong>
                    <ul>
                        <li>Start timer, then click Skip</li>
                        <li>Verify timer stops</li>
                        <li>Start timer, then click Previous</li>
                        <li>Verify timer stops and resets</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>💾 Export/Import Compatibility Testing</h2>
        
        <div class="step-list">
            <ol>
                <li><strong>Build Navigation History:</strong>
                    <ul>
                        <li>Answer 3 questions, skip 2 questions</li>
                        <li>Use Previous button to go back</li>
                        <li>Verify navigation history is working</li>
                    </ul>
                </li>
                <li><strong>Export Quiz State:</strong>
                    <ul>
                        <li>Go to Settings → Export</li>
                        <li>Save the backup file</li>
                    </ul>
                </li>
                <li><strong>Clear and Import:</strong>
                    <ul>
                        <li>Clear all data</li>
                        <li>Import the backup file</li>
                        <li>Resume the quiz</li>
                    </ul>
                </li>
                <li><strong>Verify Navigation Restored:</strong>
                    <ul>
                        <li>Check if Previous button works</li>
                        <li>Verify skipped questions are remembered</li>
                        <li>Verify navigation history is intact</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Success Criteria Checklist</h2>
        
        <div class="checklist">
            <h3>Visual Elements:</h3>
            <ul>
                <li>Four buttons visible in quiz controls</li>
                <li>Buttons properly styled and sized</li>
                <li>Icons and text clearly visible</li>
                <li>Button states (enabled/disabled) work correctly</li>
            </ul>

            <h3>Previous Question Functionality:</h3>
            <ul>
                <li>Disabled when no navigation history</li>
                <li>Enabled after answering/skipping questions</li>
                <li>Correctly restores previous question</li>
                <li>Restores correct team assignment</li>
                <li>Removes question from skipped list if applicable</li>
                <li>Stops any running timers</li>
            </ul>

            <h3>Skip Question Functionality:</h3>
            <ul>
                <li>Enabled during active questions</li>
                <li>Disabled when question already answered</li>
                <li>Moves to next question correctly</li>
                <li>Adds question to skipped list</li>
                <li>Updates team assignment (if automatic)</li>
                <li>Stops any running timers</li>
            </ul>

            <h3>Integration & Compatibility:</h3>
            <ul>
                <li>Works with both automatic and manual team assignment</li>
                <li>Compatible with export/import system</li>
                <li>Navigation history persists across page refreshes</li>
                <li>No console errors during navigation</li>
                <li>Scoring system unaffected</li>
                <li>Timer system integration works</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🐛 Common Issues to Watch For</h2>
        
        <div class="warning test-result">
            <h3>Potential Problems:</h3>
            <ul>
                <li><strong>Button States:</strong> Buttons not enabling/disabling correctly</li>
                <li><strong>Navigation History:</strong> Previous button not working or going to wrong question</li>
                <li><strong>Team Assignment:</strong> Wrong team when using Previous</li>
                <li><strong>Timer Issues:</strong> Timer not stopping when navigating</li>
                <li><strong>Scoring Problems:</strong> Points awarded incorrectly during navigation</li>
                <li><strong>Memory Leaks:</strong> Navigation history growing too large</li>
                <li><strong>Export/Import:</strong> Navigation data not saved/restored</li>
            </ul>
        </div>

        <div class="info test-result">
            <h3>Debug Tips:</h3>
            <ul>
                <li>Open Developer Console (F12) to monitor for errors</li>
                <li>Check localStorage for quiz state persistence</li>
                <li>Verify button event listeners are attached</li>
                <li>Test with different round types (questions vs anagrams)</li>
                <li>Test with multiple teams</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🎉 Final Verification</h2>
        
        <div class="pass test-result">
            <h3>If all tests pass, you should have:</h3>
            <ul>
                <li>✅ Fully functional Previous Question button</li>
                <li>✅ Fully functional Skip Question button</li>
                <li>✅ Proper button state management</li>
                <li>✅ Navigation history tracking</li>
                <li>✅ Export/import compatibility</li>
                <li>✅ No breaking changes to existing functionality</li>
                <li>✅ Enhanced quiz navigation experience</li>
            </ul>
        </div>
    </div>
</body>
</html>
