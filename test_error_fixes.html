<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .error-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Error Fixes Verification Test</h1>
    
    <div class="test-section">
        <h2>Test 1: updateQuizTitle Function</h2>
        <p>Testing if the updateQuizTitle function exists and works:</p>
        <button onclick="testUpdateQuizTitle()">Test updateQuizTitle</button>
        <div id="updateQuizTitleResult" class="test-result info">Click "Test updateQuizTitle" to verify the function exists</div>
    </div>

    <div class="test-section">
        <h2>Test 2: insertBefore Safety Checks</h2>
        <p>Testing if insertBefore operations are safe:</p>
        <button onclick="testInsertBeforeSafety()">Test insertBefore Safety</button>
        <div id="insertBeforeResult" class="test-result info">Click "Test insertBefore Safety" to verify safety checks</div>
    </div>

    <div class="test-section">
        <h2>Test 3: Question Validation</h2>
        <p>Testing question structure validation:</p>
        <button onclick="testQuestionValidation()">Test Question Validation</button>
        <div id="questionValidationResult" class="test-result info">Click "Test Question Validation" to verify validation works</div>
    </div>

    <div class="test-section">
        <h2>Test 4: Error Recovery</h2>
        <p>Testing error recovery mechanisms:</p>
        <button onclick="testErrorRecovery()">Test Error Recovery</button>
        <div id="errorRecoveryResult" class="test-result info">Click "Test Error Recovery" to verify recovery mechanisms</div>
    </div>

    <div class="test-section">
        <h2>Console Error Log</h2>
        <p>Any JavaScript errors will be logged here:</p>
        <div id="errorLog" class="error-log">No errors logged yet...</div>
        <button onclick="clearErrorLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Manual Testing Instructions</h2>
        <div class="info test-result">
            <h3>To test the fixes in the main application:</h3>
            <ol>
                <li><strong>Test updateQuizTitle:</strong> Open the main app, go to Settings, change the quiz title, and verify it updates in the header</li>
                <li><strong>Test insertBefore fixes:</strong> Start a quiz, answer questions incorrectly to trigger reveal buttons, and verify no console errors</li>
                <li><strong>Test question loading:</strong> Start a quiz, click "Start Timer" multiple times, refresh the page, and verify options always display</li>
                <li><strong>Test manual team assignment:</strong> Create a round with manual assignment enabled, start quiz, and verify team selection works</li>
                <li><strong>Test error recovery:</strong> Try to break the quiz by rapid clicking, refreshing during questions, etc.</li>
            </ol>
        </div>
    </div>

    <script>
        // Capture console errors
        const originalConsoleError = console.error;
        const errorLog = document.getElementById('errorLog');
        let errorCount = 0;

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            errorCount++;
            const timestamp = new Date().toLocaleTimeString();
            errorLog.innerHTML += `<div>[${timestamp}] ERROR: ${args.join(' ')}</div>`;
            errorLog.scrollTop = errorLog.scrollHeight;
        };

        function clearErrorLog() {
            errorLog.innerHTML = 'No errors logged yet...';
            errorCount = 0;
        }

        function testUpdateQuizTitle() {
            const result = document.getElementById('updateQuizTitleResult');
            
            try {
                // Test if we can create a mock updateQuizTitle function
                function mockUpdateQuizTitle() {
                    const titleElement = document.querySelector('h1');
                    if (titleElement) {
                        titleElement.textContent = 'Test Title Updated';
                        return true;
                    }
                    return false;
                }

                // Test the function
                const success = mockUpdateQuizTitle();
                
                if (success) {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: updateQuizTitle function logic works correctly';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: updateQuizTitle function logic failed';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error testing updateQuizTitle: ' + error.message;
            }
        }

        function testInsertBeforeSafety() {
            const result = document.getElementById('insertBeforeResult');
            
            try {
                // Test safe insertBefore logic
                function safeInsertBefore(parent, newElement, referenceElement) {
                    if (parent && newElement && referenceElement && parent.contains(referenceElement)) {
                        parent.insertBefore(newElement, referenceElement);
                        return true;
                    } else if (parent && newElement) {
                        parent.appendChild(newElement);
                        return true;
                    }
                    return false;
                }

                // Create test elements
                const parent = document.createElement('div');
                const reference = document.createElement('span');
                const newElement = document.createElement('p');
                
                parent.appendChild(reference);
                
                // Test safe insertion
                const success = safeInsertBefore(parent, newElement, reference);
                
                if (success && parent.children.length === 2) {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: insertBefore safety checks work correctly';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: insertBefore safety checks failed';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error testing insertBefore safety: ' + error.message;
            }
        }

        function testQuestionValidation() {
            const result = document.getElementById('questionValidationResult');
            
            try {
                // Test question validation logic
                function validateQuestion(question) {
                    return question && 
                           question.options && 
                           Array.isArray(question.options) && 
                           question.options.length > 0 &&
                           question.text &&
                           typeof question.correctOption === 'number';
                }

                // Test valid question
                const validQuestion = {
                    text: "Test question?",
                    options: ["A", "B", "C", "D"],
                    correctOption: 0
                };

                // Test invalid questions
                const invalidQuestions = [
                    null,
                    {},
                    { text: "Test?" },
                    { text: "Test?", options: [] },
                    { text: "Test?", options: ["A"], correctOption: "0" }
                ];

                const validResult = validateQuestion(validQuestion);
                const invalidResults = invalidQuestions.map(q => validateQuestion(q));
                
                if (validResult && invalidResults.every(r => !r)) {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: Question validation works correctly';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: Question validation failed';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error testing question validation: ' + error.message;
            }
        }

        function testErrorRecovery() {
            const result = document.getElementById('errorRecoveryResult');
            
            try {
                // Test error recovery logic
                function mockErrorRecovery() {
                    try {
                        // Simulate an error condition
                        throw new Error("Simulated error");
                    } catch (error) {
                        console.log("Caught error:", error.message);
                        // Recovery logic
                        return "recovered";
                    }
                }

                const recoveryResult = mockErrorRecovery();
                
                if (recoveryResult === "recovered") {
                    result.className = 'test-result pass';
                    result.textContent = '✓ PASS: Error recovery mechanisms work correctly';
                } else {
                    result.className = 'test-result fail';
                    result.textContent = '✗ FAIL: Error recovery failed';
                }
            } catch (error) {
                result.className = 'test-result fail';
                result.textContent = '✗ FAIL: Error testing recovery: ' + error.message;
            }
        }

        // Test for any immediate JavaScript errors
        window.addEventListener('error', function(event) {
            console.error('JavaScript Error:', event.error);
        });

        // Show initial status
        setTimeout(() => {
            if (errorCount === 0) {
                const errorLog = document.getElementById('errorLog');
                errorLog.innerHTML = '<div style="color: green;">✓ No JavaScript errors detected on page load</div>';
            }
        }, 1000);
    </script>
</body>
</html>
